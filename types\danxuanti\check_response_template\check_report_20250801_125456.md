# Round2 Response Template 检查报告

## 检查信息
- **题型**: danxuanti
- **检查时间**: 2025-08-01 12:54:56
- **总题目数**: 1080
- **发现错误数**: 6
- **修改组数**: 5
- **未修改错误数**: 1

## 检查结果概览
- ✅ **正确题目**: 1074 个
- ❌ **错误题目**: 6 个
- 🔧 **已修改**: 5 个
- ⏭️ **未修改**: 1 个

## 所有发现的错误

### 第116组 (题目2) - 🔧 已修改

- **题目2**: `'C'` vs `'F'` → 模型:True, 应该:False

### 第127组 (题目4) - 🔧 已修改

- **题目4**: `'A'` vs `'A'` → 模型:False, 应该:True

### 第142组 (题目1) - 🔧 已修改

- **题目1**: `'C'` vs `'C'` → 模型:False, 应该:True

### 第191组 (题目1) - 🔧 已修改

- **题目1**: `'B'` vs `'B'` → 模型:False, 应该:True

### 第193组 (题目5) - ⏭️ 未修改

- **题目5**: `'G'` vs `'F'` → 模型:True, 应该:False

### 第213组 (题目4) - 🔧 已修改

- **题目4**: `'F'` vs `'F'` → 模型:False, 应该:True

## 未修改的错误汇总

### 第193组 (题目5)

- **题目5**: `'G'` vs `'F'` → 模型:True, 应该:False

## 检查说明

### 数据源
- **学生答案**: `types/danxuanti/response/response_template.md` 中的"响应内容"
- **正确答案**: `types/danxuanti/response/answer.md` 中的"响应内容"
- **模型判断**: `types/danxuanti/round2_response/response_template.md` 中的"模型回答"

### 比对规则
- 按顺序比对JSON中的value，忽略键名差异
- 学生答案与正确答案字符串完全相同 → 期望判断为true
- 学生答案与正确答案字符串不相同 → 期望判断为false

### 修改说明
- 🔧 **已修改**: 表示该组的错误已通过脚本自动修改
- ⏭️ **未修改**: 表示该组的错误被用户跳过，未进行修改

---
*报告生成时间: 2025-08-01 12:54:56*
